<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程案例 - 华隆重工</title>
    <meta name="description" content="华隆重工工程案例展示，包括钢铁冶金、电力、矿山、建材等行业的成功项目案例。">
    <meta name="keywords" content="工程案例,华隆重工案例,重型机械项目,成功案例">
    
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- TailwindCSS Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1E3A8A',
                        'secondary': '#6B7280',
                        'accent': '#F97316',
                        'light-gray': '#F3F4F6',
                        'dark-gray': '#111827'
                    },
                    fontFamily: {
                        'sans': ['Noto Sans SC', 'PingFang SC', 'Source Han Sans', 'Microsoft YaHei', 'sans-serif'],
                        'en': ['Montserrat', 'Roboto', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans text-dark-gray bg-white">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16 lg:h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center">
                        <img src="images/logo.png" alt="华隆重工" class="h-10 lg:h-12 w-auto">
                        <span class="ml-3 text-xl lg:text-2xl font-bold text-primary">华隆重工</span>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex space-x-8">
                    <a href="index.html" class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">首页</a>
                    <a href="products/" class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">产品中心</a>
                    <a href="services/" class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">客户服务</a>
                    <a href="cases.html" class="text-primary font-medium">工程案例</a>
                    <a href="about/" class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">走进华隆</a>
                    <a href="careers/" class="text-dark-gray hover:text-primary transition-colors duration-200 font-medium">人才招聘</a>
                </nav>
                
                <!-- Language Switcher & Contact Button -->
                <div class="flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative group">
                        <button class="flex items-center text-secondary hover:text-primary transition-colors duration-200">
                            <i class="fas fa-globe mr-2"></i>
                            <span class="hidden sm:inline">中文</span>
                            <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full right-0 mt-2 w-32 bg-white shadow-lg rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">العربية</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray bg-light-gray">中文</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">English</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">Français</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">Русский</a>
                            <a href="#" class="block px-4 py-2 text-sm text-dark-gray hover:bg-light-gray">Español</a>
                        </div>
                    </div>
                    
                    <!-- Contact Button -->
                    <a href="about/contact.html" class="bg-accent text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200 font-medium">
                        联系我们
                    </a>
                    
                    <!-- Mobile Menu Button -->
                    <button class="lg:hidden text-dark-gray hover:text-primary" id="mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <nav class="bg-light-gray py-4" aria-label="面包屑导航">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="index.html" class="text-secondary hover:text-primary">首页</a></li>
                <li><i class="fas fa-chevron-right text-secondary text-xs"></i></li>
                <li class="text-dark-gray font-medium">工程案例</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-primary mb-6">工程案例</h1>
                <p class="text-lg text-secondary max-w-3xl mx-auto">
                    华隆重工在钢铁冶金、电力、矿山、建材等行业拥有丰富的项目经验，为客户提供优质的设备和服务
                </p>
            </div>

            <!-- Filter Tabs -->
            <div class="flex flex-wrap justify-center mb-12">
                <button class="filter-tab active px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200" data-filter="all">
                    全部案例
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200" data-filter="steel">
                    钢铁冶金
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200" data-filter="power">
                    电力行业
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200" data-filter="mining">
                    矿山开采
                </button>
                <button class="filter-tab px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-colors duration-200" data-filter="building">
                    建材行业
                </button>
            </div>

            <!-- Cases Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="cases-grid">
                <!-- Case 1 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="steel">
                    <div class="relative overflow-hidden">
                        <img src="images/products/img__201018155050148.jpg" alt="钢铁厂斗轮机项目" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">钢铁冶金</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">某大型钢铁厂斗轮机项目</h3>
                        <p class="text-secondary mb-4">为客户提供了大型斗轮堆取料机设备，显著提高了原料处理效率，年处理能力达到500万吨。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2023年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm" onclick="openCaseModal('case1')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 2 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="power">
                    <div class="relative overflow-hidden">
                        <img src="images/products/img__201018155115303.jpg" alt="火电厂输送设备" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">电力行业</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">火电厂燃料输送系统</h3>
                        <p class="text-secondary mb-4">为火电厂设计制造了完整的燃料输送系统，包括卸车机、输送带等设备，运行稳定可靠。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2023年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm" onclick="openCaseModal('case2')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 3 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="mining">
                    <div class="relative overflow-hidden">
                        <img src="images/products/img__201018155138197.jpg" alt="矿山开采设备" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">矿山开采</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">大型矿山堆取料设备</h3>
                        <p class="text-secondary mb-4">为大型露天矿山提供了混匀堆取料机，有效提高了矿石处理效率和质量均匀性。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2022年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm" onclick="openCaseModal('case3')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 4 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="building">
                    <div class="relative overflow-hidden">
                        <img src="images/products/img__20101916383346.jpg" alt="建材厂矫直设备" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">建材行业</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">建材厂板材矫直生产线</h3>
                        <p class="text-secondary mb-4">为建材企业提供了先进的板材矫直机，大幅提高了产品质量和生产效率。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2022年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm" onclick="openCaseModal('case4')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 5 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="steel">
                    <div class="relative overflow-hidden">
                        <img src="images/products/img__201019163443828.jpg" alt="钢厂卸车机" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">钢铁冶金</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">钢厂原料卸车系统</h3>
                        <p class="text-secondary mb-4">为钢铁企业设计制造了高效的链斗卸车机系统，大幅提升了原料卸车效率。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2021年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm" onclick="openCaseModal('case5')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case 6 -->
                <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden" data-category="power">
                    <div class="relative overflow-hidden">
                        <img src="images/products/img__201019164030240.jpg" alt="电厂链篦机" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">电力行业</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-primary mb-3">电厂球团生产线</h3>
                        <p class="text-secondary mb-4">为电厂提供了链篦机设备，用于球团生产，设备运行稳定，产品质量优良。</p>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-secondary">
                                <i class="fas fa-calendar mr-1"></i>
                                2021年
                            </div>
                            <button class="text-accent hover:text-orange-600 font-medium text-sm" onclick="openCaseModal('case6')">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="mt-16 bg-primary rounded-lg p-8 text-white">
                <h2 class="text-3xl font-bold mb-8 text-center">项目成果</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">100+</div>
                        <div class="text-blue-200">成功项目</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">50+</div>
                        <div class="text-blue-200">合作客户</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">20+</div>
                        <div class="text-blue-200">年行业经验</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">99%</div>
                        <div class="text-blue-200">客户满意度</div>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="mt-16 text-center">
                <h2 class="text-3xl font-bold text-primary mb-4">开始您的项目</h2>
                <p class="text-lg text-secondary mb-8 max-w-2xl mx-auto">
                    如果您有重型机械设备需求，欢迎联系我们，我们将为您提供专业的解决方案。
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="about/contact.html" class="bg-accent text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition-colors duration-200 font-medium">
                        <i class="fas fa-phone mr-2"></i>
                        联系我们
                    </a>
                    <a href="products/" class="bg-primary text-white px-8 py-3 rounded-lg hover:bg-blue-800 transition-colors duration-200 font-medium">
                        <i class="fas fa-cog mr-2"></i>
                        查看产品
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark-gray text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-1">
                    <div class="flex items-center mb-4">
                        <img src="images/logo.png" alt="华隆重工" class="h-8 w-auto">
                        <span class="ml-3 text-xl font-bold">华隆重工</span>
                    </div>
                    <p class="text-gray-400 mb-4">专业致力于重型机械设备的设计、研发、制造、销售和服务</p>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="about/company.html" class="text-gray-400 hover:text-white transition-colors duration-200">华隆简介</a></li>
                        <li><a href="products/" class="text-gray-400 hover:text-white transition-colors duration-200">产品中心</a></li>
                        <li><a href="cases.html" class="text-gray-400 hover:text-white transition-colors duration-200">工程案例</a></li>
                        <li><a href="careers/" class="text-gray-400 hover:text-white transition-colors duration-200">人才招聘</a></li>
                    </ul>
                </div>
                
                <!-- Services -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">客户服务</h3>
                    <ul class="space-y-2">
                        <li><a href="services/technical-renovation.html" class="text-gray-400 hover:text-white transition-colors duration-200">技术改造</a></li>
                        <li><a href="services/spare-parts.html" class="text-gray-400 hover:text-white transition-colors duration-200">产品备件</a></li>
                        <li><a href="about/contact.html" class="text-gray-400 hover:text-white transition-colors duration-200">联系我们</a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系信息</h3>
                    <div class="space-y-2 text-gray-400">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-3"></i>
                            <span>湖南长沙环保科技产业园</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-3"></i>
                            <span>+86-731-XXXXXXX</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-3"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 长沙华隆重型机器制造有限公司. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            const caseCards = document.querySelectorAll('.case-card');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // Update active tab
                    filterTabs.forEach(t => {
                        t.classList.remove('active', 'bg-primary', 'text-white');
                        t.classList.add('bg-light-gray', 'text-secondary');
                    });
                    this.classList.add('active', 'bg-primary', 'text-white');
                    this.classList.remove('bg-light-gray', 'text-secondary');
                    
                    // Filter cases
                    caseCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-category') === filter) {
                            card.style.display = 'block';
                            card.classList.add('animate-fade-in-up');
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
            
            // Initialize first tab as active
            filterTabs[0].classList.add('active', 'bg-primary', 'text-white');
            filterTabs.forEach((tab, index) => {
                if (index > 0) {
                    tab.classList.add('bg-light-gray', 'text-secondary');
                }
            });
        });
        
        // Case modal functionality
        function openCaseModal(caseId) {
            showNotification('案例详情功能将在后续版本中完善', 'info');
        }
    </script>
</body>
</html>
