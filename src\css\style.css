/* Custom CSS for Hualong Heavy Industry Website */

/* Global Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

/* Hero Section Background */
.hero-bg {
    background-image: linear-gradient(rgba(30, 58, 138, 0.8), rgba(30, 58, 138, 0.8)), 
                      url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* Product Cards */
.product-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.product-card img {
    transition: transform 0.3s ease;
}

.product-card:hover img {
    transform: scale(1.05);
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.service-icon {
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
    color: #F97316;
}

/* Navigation Dropdown */
.dropdown-menu {
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.group:hover .dropdown-menu {
    transform: translateY(0);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #1E3A8A, #3B82F6);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1E40AF, #2563EB);
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(30, 58, 138, 0.3);
}

.btn-accent {
    background: linear-gradient(135deg, #F97316, #FB923C);
    transition: all 0.3s ease;
}

.btn-accent:hover {
    background: linear-gradient(135deg, #EA580C, #F97316);
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(249, 115, 22, 0.3);
}

/* Image Gallery */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.02);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: flex-end;
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Technical Specifications Table */
.tech-table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.tech-table th {
    background: #1E3A8A;
    color: white;
    padding: 0.75rem;
    text-align: center;
    font-weight: 600;
}

.tech-table td {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
}

.tech-table tr:nth-child(even) {
    background: #f9fafb;
}

.tech-table tr:hover {
    background: #f3f4f6;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1E3A8A;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
    .hero-bg {
        background-attachment: scroll;
    }
    
    .tech-table {
        font-size: 0.875rem;
    }
    
    .tech-table th,
    .tech-table td {
        padding: 0.5rem;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #F97316;
    outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #1E3A8A;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    img {
        max-width: 100% !important;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #1E3A8A;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1E40AF;
}
